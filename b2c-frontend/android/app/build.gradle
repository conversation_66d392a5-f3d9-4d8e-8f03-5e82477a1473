apply plugin: "com.android.application"
apply plugin: 'com.google.gms.google-services'
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

project.ext.envConfigFiles = [
    debug: ".env",
    release: ".env",
]
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

react {
    autolinkLibrariesWithApp()
}

def enableProguardInReleaseBuilds = false
def jscFlavor = 'org.webkit:android-jsc:+'
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}
apply from: new File(["node", "--print", "require.resolve('@sentry/react-native/package.json')"].execute().text.trim(), "../sentry.gradle")
android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.navicater"
    defaultConfig {
        applicationId "com.navicater"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        
        def vc = (project.findProperty("VERSION_CODE") ?: "25040526").toString().toInteger()
        def vn = (project.findProperty("VERSION_NAME") ?: "0.0.26").toString()
        versionCode vc
        versionName vn
        println("→ ANDROID using VERSION_CODE=${vc} VERSION_NAME=${vn}")
    }

    signingConfigs {
        debug {
            storeFile file("debug.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
        }
        release {
            storeFile file("release.keystore")
            storePassword System.getenv("KEYSTORE_PASSWORD")
            keyAlias System.getenv("KEYSTORE_ALIAS")
            keyPassword System.getenv("KEY_PASSWORD")
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            signingConfig signingConfigs.release

            // ✅ NEW: CI-controllable toggles (fallback to your existing flag)
            def minify = project.hasProperty("MINIFY")
                ? project.property("MINIFY").toBoolean()
                : enableProguardInReleaseBuilds
            def shrink = project.hasProperty("SHRINK")
                ? project.property("SHRINK").toBoolean()
                : true

            minifyEnabled minify
            shrinkResources (shrink && minify)
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }
}

dependencies {
    implementation("com.facebook.react:react-android")
    implementation project(':react-native-config')
    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

// --- Ensure Codegen runs before any CMake/NDK tasks (permanent hardening) ---
gradle.projectsEvaluated {
  // Variant-agnostic task (RN 0.73+)
  def genAny = tasks.findByName("generateCodegenArtifactsFromSchema")

  if (genAny != null) {
    tasks.matching { t ->
      t.name == "preBuild" ||
      t.name.startsWith("configureCMake") ||
      t.name.startsWith("externalNativeBuild") ||
      t.name.startsWith("buildCMake")
    }.configureEach { it.dependsOn(genAny) }
  } else {
    // Fallback: wire per-variant if needed
    ["Debug", "Release"].each { v ->
      def gen = tasks.findByName("generateCodegenArtifactsFromSchema${v}")
      if (gen != null) {
        tasks.matching { t ->
          t.name in ["pre${v}Build"] ||   // <- correct task name
          t.name.startsWith("configureCMake") ||
          t.name.startsWith("externalNativeBuild") ||
          t.name.startsWith("buildCMake")
        }.configureEach { it.dependsOn(gen) }
      }
    }
  }
}

