name: 'Build Android App'
description: 'Builds an Android AAB'

inputs:
  version:
    description: 'Semantic version'
    required: true
  build-number:
    description: 'Build number'
    required: true
  keystore-password:
    description: 'Keystore password'
    required: true
  keystore-alias:
    description: 'Keystore alias'
    required: true
  key-password:
    description: 'Key password'
    required: true
  keystore-base64:
    description: 'Base64-encoded keystore'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Install Node.js Dependencies
      shell: bash
      run: |
        echo "📦 Installing Node.js dependencies"
        npm ci

    - name: Assert JS deps present before Gradle
      shell: bash
      run: |
        set -e
        test -d node_modules
        test -f node_modules/@react-native/gradle-plugin/package.json
        echo "✅ node_modules and RN gradle plugin present"

    - name: Action env
      shell: bash
      run: |
        echo "BASE_URL: ${{ env.BASE_URL }}"

    - name: Create .env file
      shell: bash
      run: |
        echo "🔐 Creating .env file"
        cat > .env <<EOF
        API_KEY=${{ env.API_KEY }}
        BASE_URL=${{ env.BASE_URL }}
        AI_URL=${{ env.AI_URL }}
        FIREBASE_ANDROID_APP_ID=${{ env.FIREBASE_ANDROID_APP_ID }}
        FIREBASE_API_KEY=${{ env.FIREBASE_API_KEY }}
        FIREBASE_AUTH_DOMAIN=${{ env.FIREBASE_AUTH_DOMAIN }}
        FIREBASE_DATABASE_URL=${{ env.FIREBASE_DATABASE_URL }}
        FIREBASE_IOS_APP_ID=${{ env.FIREBASE_IOS_APP_ID }}
        FIREBASE_MESSAGING_SENDER_ID=${{ env.FIREBASE_MESSAGING_SENDER_ID }}
        FIREBASE_PROJECT_ID=${{ env.FIREBASE_PROJECT_ID }}
        FIREBASE_STORAGE_BUCKET=${{ env.FIREBASE_STORAGE_BUCKET }}
        IOS_CLIENT_ID=${{ env.IOS_CLIENT_ID }}
        SENTRY_DSN_URL=${{ env.SENTRY_DSN_URL }}
        WEB_CLIENT_ID=${{ env.WEB_CLIENT_ID }}
        ENV=${{ env.ENV }}
        MAPBOX_ACCESS_TOKEN=${{ env.MAPBOX_ACCESS_TOKEN }}
        EOF

    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        cache: 'gradle'

    - name: Assert Gradle codegen wiring present
      shell: bash
      run: |
        set -e
        grep -q "gradle.projectsEvaluated" android/app/build.gradle
        grep -q "generateCodegenArtifactsFromSchema" android/app/build.gradle
        echo "✅ Codegen wiring present in android/app/build.gradle"

    - name: Decode Keystore
      shell: bash
      run: |
        echo "🔓 Decoding keystore"
        echo "${KEYSTORE_BASE64}" | base64 --decode > android/app/release.keystore
        ls -l android/app/release.keystore || { echo "❌ Keystore file missing!"; exit 1; }
      env:
        KEYSTORE_BASE64: ${{ inputs.keystore-base64 }}

    - name: Generate RN Codegen (app + any modules)
      shell: bash
      run: |
        set -e
        echo "🧬 Generating React Native codegen artifacts"
        cd android

        # Always include app-level codegen
        TASKS=":app:generateCodegenArtifactsFromSchema"

        # Collect any module codegen tasks that actually exist (handles variant suffixes)
        PROJECTS=$(./gradlew -q projects | awk -F"'" '/Project/{print $2}' | grep '^:' || true)
        for P in $PROJECTS; do
          MOD_TASKS=$(./gradlew -q ${P}:tasks --all | awk '/^generateCodegenArtifactsFromSchema[[:alnum:]]*/{print $1}' || true)
          for T in $MOD_TASKS; do TASKS="$TASKS ${P}:$T"; done
        done

        echo "➡️ Running tasks: $TASKS"
        ./gradlew -PnewArchEnabled=true $TASKS --no-daemon --stacktrace

        # Optional: verify autolinking CMake no longer references react-native-config
        FILE="app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake"
        if [ -f "$FILE" ] && grep -q "react-native-config" "$FILE"; then
          echo "❌ react-native-config still appears in autolinking CMake. Check react-native.config.js and settings.gradle."
          sed -n '1,120p' "$FILE"
          exit 1
        fi

    - name: Build Android Release AAB
      shell: bash
      run: |
        echo "🏗️ Building Android AAB"
        cd android
        ./gradlew -PnewArchEnabled=true \
          -PVERSION_CODE='${{ inputs.build-number }}' \
          -PVERSION_NAME='${{ inputs.version }}' \
          bundleRelease --no-daemon --stacktrace --info

      env:
        KEYSTORE_PASSWORD: ${{ inputs.keystore-password }}
        KEYSTORE_ALIAS: ${{ inputs.keystore-alias }}
        KEY_PASSWORD: ${{ inputs.key-password }}

    - name: Upload Release AAB artifact
      uses: actions/upload-artifact@v4
      with:
        name: app-release-bundle
        path: android/app/build/outputs/bundle/release/app-release.aab
        retention-days: 7
