import { useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import { JobRequirementsFormDataI } from "./types";
import { showToast } from "@/src/utilities/toast";
import { createJobStageTwoAPI, fetchDraftJobAPI, updateJobRequirementsAPI, fetchJobRequirementsForEditingAPI } from "@/src/networks/jobs/createJob";
import { fetchJobForCandidate } from "@/src/networks/company/job/job";
import { CommonActions, useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { CareerStackParamListI } from "@/src/navigation/types";
import { useSelector } from "react-redux";
import { selectCurrentUser } from "@/src/redux/selectors/user";

export const useJobRequirements = (jobId?:string, editing?: boolean) => {
    const navigation = useNavigation<StackNavigationProp<CareerStackParamListI>>();
    const currentUser = useSelector(selectCurrentUser);
    const [modalVisible,setModalVisible] = useState(false);
    const [isSubmitting,setIsSubmitting] = useState(false);
    const [hasAdvancedRequirements, setHasAdvancedRequirements] = useState(false);
    const [hasAdvancedBenefits, setHasAdvancedBenefits] = useState(false);
    const methods = useForm<JobRequirementsFormDataI>({
        mode: 'onChange',
        defaultValues: {
          about: '',
          rolesResponsibilities: '',
          requirements: '',
          benefits: ''
        },
      });
    
    useEffect(() => {
        const fetchJobData = async () => {
            if (!editing) {
                methods.reset({
                    about: '',
                    rolesResponsibilities: '',
                    requirements: '',
                    benefits: ''
                });
                setHasAdvancedRequirements(false);
                setHasAdvancedBenefits(false);
            } else if (editing && jobId) {
                try {
                    const jobData = await fetchJobRequirementsForEditingAPI(jobId);

                    if (jobData) {
                        // Set requirements data
                        methods.setValue('about', jobData.about || '');
                        methods.setValue('rolesResponsibilities', jobData.rolesResponsibilities || '');
                        methods.setValue('requirements', jobData.requirements || '');
                        methods.setValue('benefits', jobData.benefits || '');

                        // Set advanced detection flags
                        setHasAdvancedRequirements(jobData.hasAdvancedRequirements || false);
                        setHasAdvancedBenefits(jobData.hasAdvancedBenefits || false);
                    }
                } catch (error) {
                    showToast({
                        type: 'error',
                        message: 'Failed to fetch job requirements'
                    });
                }
            }
        };

        fetchJobData();
    }, [jobId, editing, methods])

    const onSubmit = async () => {
        try{
            setIsSubmitting(true)
            const data = methods.getValues()
            const payload = {
                jobId: jobId!,
                ...data
            };
            
            let result;
            if (editing && jobId) {
                result = await updateJobRequirementsAPI(jobId, payload, true);
            } else {
                result = await createJobStageTwoAPI(jobId!, payload);
            }
            return result.id
        }catch(e){
            showToast({
                type:'error',
                message: editing ? 'Failed to Update Job Requirements. Try After sometime' : 'Failed to Save Job Post. Try After sometime'
            })
        }finally{
            setIsSubmitting(false)
        }
        
    }
    
    const onGeneratePress = async() => {
        const jobId = await onSubmit();
        if(jobId){
            if (editing) {
                navigation.navigate('CareerStack', {
                    screen: currentUser.isActive ? 'Careers' : 'JobPosts',
                    params: { refresh: true }
                });
                showToast({
                    type: 'success',
                    message: 'Job updated successfully'
                });
            } else {
                navigation.dispatch(
                    CommonActions.reset({
                        index: 0,
                        routes: [
                            {
                                name: 'CareerStack',
                                state: {
                                    routes: [
                                        {
                                            name:  currentUser.isActive ? 'Careers' : 'JobPosts',
                                            params: { jobId }
                                        },
                                    ],
                                },
                            },
                        ],
                    }),
                );
            }
        }
    }

    const handleToggleClick = () => {
        setModalVisible(true)
    }

    const handleModalClose = () => {
        setModalVisible(false)
    }

    return {
        methods,
        modalVisible,
        handleToggleClick,
        handleModalClose,
        onGeneratePress,
        hasAdvancedRequirements,
        hasAdvancedBenefits,
        isAdvanced: hasAdvancedRequirements || hasAdvancedBenefits
    }
}