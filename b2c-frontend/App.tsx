import type React from 'react';
import { useEffect, useState } from 'react';
import { LogBox, AppState, Linking, BackHandler, Platform } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import Config from 'react-native-config';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { configureReanimatedLogger, ReanimatedLogLevel } from 'react-native-reanimated';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import VersionCheck from 'react-native-version-check';
import { PersistGate } from 'redux-persist/integration/react';
import * as Sentry from '@sentry/react-native';
import { Provider } from 'react-redux';
import { toastConfig } from '@/src/components/CustomToast/config';
import CustomModal from '@/src/components/Modal';
import { persistor, store } from '@/src/redux/store';
import { navigationRef } from '@/src/utilities/navigation';
import AppNavigator from '@/src/navigation';
import '@/styles/global.css';
import NavicaterProvider from './src/context';
import { useDeepLink } from './src/deeplink/useDeepLink';
import useStorage from './src/hooks/storage';
import 'react-native-worklets-core';


LogBox.ignoreAllLogs();
console.warn = () => {};

Sentry.init({
  dsn: Config.SENTRY_DSN_URL,
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1,
  integrations: [Sentry.mobileReplayIntegration()],
});

configureReanimatedLogger({ level: ReanimatedLogLevel.warn, strict: false });

const NavigationContent = (): React.JSX.Element => {
  useDeepLink();

  return (
    <SafeAreaProvider style={{ backgroundColor: 'white' }}>
      <AppNavigator />
      <Toast config={toastConfig} />
    </SafeAreaProvider>
  );
};

const AppContent = (): React.JSX.Element => {
  return (
    <NavicaterProvider>
      <NavigationContainer ref={navigationRef}>
        <NavigationContent />
      </NavigationContainer>
    </NavicaterProvider>
  );
};

const openStore = async (url: string) => {
  try {
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
    }
  } catch (err) {}
};

const App = (): React.JSX.Element => {
  const { clearAllStorage } = useStorage();
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [storeUrl, setStoreUrl] = useState('');

  useEffect(() => {
    const checkVersion = async () => {
      try {
        const updateNeeded = await VersionCheck.needUpdate();
        if (updateNeeded?.isNeeded) {
          setStoreUrl(updateNeeded.storeUrl);
          setShowUpdateModal(true);
        }
      } catch (e) {}
    };
    checkVersion();
  }, []);

  useEffect(() => {
    const sub = AppState.addEventListener('change', (state) => {
      if (state === 'active') {
        VersionCheck.needUpdate().then((updateNeeded) => {
          if (updateNeeded?.isNeeded) {
            setStoreUrl(updateNeeded.storeUrl);
            setShowUpdateModal(true);
          }
        });
      }
    });
    return () => sub.remove();
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Provider store={store}>
        <PersistGate persistor={persistor} loading={null}>
          <AppContent />
          <CustomModal
            isVisible={showUpdateModal}
            title="Please Update"
            description="You will have to update your app to the latest version to continue using."
            confirmText="Update"
            onConfirm={() => {
              setShowUpdateModal(false);
              if (storeUrl) {
                openStore(storeUrl);
              }
            }}
            onCancel={() => {
              if (Platform.OS === 'android') {
                BackHandler.exitApp();
              }
            }}
          />
        </PersistGate>
      </Provider>
    </GestureHandlerRootView>
  );
};

export default Sentry.wrap(App);
